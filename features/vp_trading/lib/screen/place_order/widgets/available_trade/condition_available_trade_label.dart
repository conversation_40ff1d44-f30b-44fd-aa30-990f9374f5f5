import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

class ConditionAvailableTradeLabel extends StatelessWidget {
  const ConditionAvailableTradeLabel({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PlaceOrderCubit, PlaceOrderState>(
      builder: (context, statePlaceOrder) {
        return BlocBuilder<AvailableTradeCubit, AvailableTradeState>(
          builder: (context, state) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        VPTradingLocalize.current.trading_capital_cost_price,
                        style: vpTextStyle.captionRegular.copyColor(
                          vpColor.textSecondary,
                        ),
                      ),
                      const SizedBox(width: 10),
                      BlocBuilder<
                        ValidateConditionOrderCubit,
                        ValidateConditionOrderState
                      >(
                        builder: (context, state) {
                          return Text(
                            state.costPriceDisplay,
                            style: vpTextStyle.captionSemiBold.copyColor(
                              vpColor.textPrimary,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        VPTradingLocalize.current.trading_volume_holding,
                        style: vpTextStyle.captionRegular.copyColor(
                          vpColor.textSecondary,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Text(
                        state.availableTrade?.maxSellQty?.toMoney(
                              showSymbol: false,
                            ) ??
                            "",
                        style: vpTextStyle.captionSemiBold.copyColor(
                          vpColor.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
